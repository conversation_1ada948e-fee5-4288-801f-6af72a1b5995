import pandas as pd
import numpy as np
from sklearn.tree import DecisionTreeRegressor, DecisionTreeClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, r2_score, accuracy_score, classification_report
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class RiskAwareDecisionTree:
    """融合风险管理的决策树模型"""
    
    def __init__(self, false_negative_cost=10, false_positive_cost=1, 
                 detection_threshold=0.04, confidence_level=0.95):
        """
        初始化风险感知决策树
        
        Parameters:
        - false_negative_cost: 假阴性成本（漏检的代价）
        - false_positive_cost: 假阳性成本（误检的代价）
        - detection_threshold: 检测阈值
        - confidence_level: 置信水平
        """
        self.false_negative_cost = false_negative_cost
        self.false_positive_cost = false_positive_cost
        self.detection_threshold = detection_threshold
        self.confidence_level = confidence_level
        self.risk_models = {}
        self.success_rate_models = {}
        
    def calculate_detection_success_rate(self, df, bmi_group, time_window):
        """计算特定BMI组在特定时间窗口的检测成功率"""
        group_data = df[df['BMI组'] == bmi_group]
        
        # 在时间窗口内的数据
        window_data = group_data[
            (group_data['检测孕周'] >= time_window[0]) & 
            (group_data['检测孕周'] <= time_window[1])
        ]
        
        if len(window_data) == 0:
            return 0.0, 0.0
            
        success_rate = (window_data['Y染色体浓度'] > self.detection_threshold).mean()
        
        # 计算置信区间
        n = len(window_data)
        if n > 0:
            se = np.sqrt(success_rate * (1 - success_rate) / n)
            ci_lower = success_rate - stats.norm.ppf((1 + self.confidence_level) / 2) * se
            ci_upper = success_rate + stats.norm.ppf((1 + self.confidence_level) / 2) * se
            ci_width = ci_upper - ci_lower
        else:
            ci_width = 1.0
            
        return success_rate, ci_width
    
    def calculate_expected_risk(self, success_rate, ci_width):
        """计算期望风险"""
        # 考虑不确定性的风险计算
        false_negative_risk = (1 - success_rate + ci_width/2) * self.false_negative_cost
        false_positive_risk = (success_rate + ci_width/2) * self.false_positive_cost
        
        total_risk = false_negative_risk + false_positive_risk
        return total_risk, false_negative_risk, false_positive_risk
    
    def find_optimal_detection_window(self, df, bmi_group, min_week=10, max_week=25):
        """为特定BMI组找到最优检测窗口"""
        best_risk = float('inf')
        best_window = None
        best_strategy = None
        
        risk_analysis = []
        
        # 评估不同的检测窗口
        for start_week in range(min_week, max_week-1):
            for window_size in [1, 2, 3, 4]:  # 1-4周的检测窗口
                end_week = min(start_week + window_size, max_week)
                
                success_rate, ci_width = self.calculate_detection_success_rate(
                    df, bmi_group, (start_week, end_week)
                )
                
                if success_rate == 0:
                    continue
                    
                total_risk, fn_risk, fp_risk = self.calculate_expected_risk(success_rate, ci_width)
                
                risk_analysis.append({
                    'start_week': start_week,
                    'end_week': end_week,
                    'window_size': window_size,
                    'success_rate': success_rate,
                    'confidence_interval_width': ci_width,
                    'total_risk': total_risk,
                    'false_negative_risk': fn_risk,
                    'false_positive_risk': fp_risk
                })
                
                if total_risk < best_risk:
                    best_risk = total_risk
                    best_window = (start_week, end_week)
                    best_strategy = {
                        'primary_time': start_week + window_size/2,
                        'window': (start_week, end_week),
                        'success_rate': success_rate,
                        'total_risk': total_risk,
                        'confidence_interval': ci_width
                    }
        
        return best_strategy, pd.DataFrame(risk_analysis)
    
    def generate_multi_stage_strategy(self, df, bmi_group):
        """生成多阶段检测策略"""
        # 第一阶段：找到最优主检测时间
        primary_strategy, risk_df = self.find_optimal_detection_window(df, bmi_group)
        
        if primary_strategy is None:
            return None
            
        # 第二阶段：如果主检测失败，制定备选策略
        backup_strategies = []
        
        # 在主检测时间之后寻找备选时间
        primary_end = primary_strategy['window'][1]
        
        for backup_start in range(int(primary_end) + 1, 25, 2):
            backup_end = min(backup_start + 2, 25)
            
            success_rate, ci_width = self.calculate_detection_success_rate(
                df, bmi_group, (backup_start, backup_end)
            )
            
            if success_rate > 0.7:  # 只考虑成功率较高的备选方案
                total_risk, fn_risk, fp_risk = self.calculate_expected_risk(success_rate, ci_width)
                
                backup_strategies.append({
                    'backup_time': (backup_start + backup_end) / 2,
                    'window': (backup_start, backup_end),
                    'success_rate': success_rate,
                    'total_risk': total_risk
                })
        
        # 选择风险最低的备选策略
        if backup_strategies:
            best_backup = min(backup_strategies, key=lambda x: x['total_risk'])
        else:
            best_backup = None
            
        return {
            'bmi_group': bmi_group,
            'primary_strategy': primary_strategy,
            'backup_strategy': best_backup,
            'risk_analysis': risk_df
        }
    
    def fit_and_predict(self, df):
        """训练模型并生成风险感知的检测策略"""
        # 数据预处理
        df_clean = df.dropna(subset=['孕妇代码', '检测孕周', '孕妇BMI', 'Y染色体浓度'])
        
        # 定义BMI分组
        def get_bmi_group(bmi):
            if 20 <= bmi < 28: return '[20,28)'
            elif 28 <= bmi < 32: return '[28,32)'
            elif 32 <= bmi < 36: return '[32,36)'
            elif 36 <= bmi < 40: return '[36,40)'
            elif bmi >= 40: return '≥40'
            else: return '<20'
        
        df_clean = df_clean.copy()
        df_clean['BMI组'] = df_clean['孕妇BMI'].apply(get_bmi_group)
        
        # 为每个BMI组生成风险感知策略
        strategies = {}
        
        for bmi_group in df_clean['BMI组'].unique():
            if bmi_group == '<20':  # 跳过样本太少的组
                continue
                
            group_data = df_clean[df_clean['BMI组'] == bmi_group]
            if len(group_data) < 20:  # 确保有足够样本
                continue
                
            print(f"\n分析 {bmi_group} 组...")
            strategy = self.generate_multi_stage_strategy(df_clean, bmi_group)
            
            if strategy:
                strategies[bmi_group] = strategy
                
        return strategies
    
    def print_risk_aware_recommendations(self, strategies):
        """打印风险感知的检测建议"""
        print("\n" + "="*80)
        print("风险感知的产前检测策略建议")
        print("="*80)
        
        for bmi_group, strategy in strategies.items():
            print(f"\n【{bmi_group} BMI组】")
            
            primary = strategy['primary_strategy']
            print(f"主要检测策略:")
            print(f"  推荐检测时间: {primary['primary_time']:.1f}周")
            print(f"  检测窗口: {primary['window'][0]}-{primary['window'][1]}周")
            print(f"  预期成功率: {primary['success_rate']:.2%}")
            print(f"  总风险评分: {primary['total_risk']:.2f}")
            print(f"  置信区间宽度: ±{primary['confidence_interval']:.3f}")
            
            backup = strategy['backup_strategy']
            if backup:
                print(f"备选检测策略:")
                print(f"  备选检测时间: {backup['backup_time']:.1f}周")
                print(f"  检测窗口: {backup['window'][0]}-{backup['window'][1]}周")
                print(f"  预期成功率: {backup['success_rate']:.2%}")
                print(f"  备选风险评分: {backup['total_risk']:.2f}")
            else:
                print(f"备选检测策略: 无合适的备选方案")
                
            # 风险解释
            if primary['total_risk'] < 2:
                risk_level = "低风险"
            elif primary['total_risk'] < 5:
                risk_level = "中等风险"
            else:
                risk_level = "高风险"
                
            print(f"  风险等级: {risk_level}")
            
        print("\n" + "="*80)
        print("风险管理说明:")
        print(f"- 假阴性成本权重: {self.false_negative_cost} (漏检的代价)")
        print(f"- 假阳性成本权重: {self.false_positive_cost} (误检的代价)")
        print(f"- 检测阈值: {self.detection_threshold}")
        print(f"- 置信水平: {self.confidence_level}")
        print("- 策略优化目标: 最小化期望总风险")
        print("="*80)

def main():
    # 加载数据
    df = pd.read_excel(r'D:\数学建模\2025国赛C题\男胎数据.xlsx')
    print(f"数据形状: {df.shape}")
    
    # 创建风险感知决策树模型
    # 假阴性成本设置较高，因为漏检的后果更严重
    risk_model = RiskAwareDecisionTree(
        false_negative_cost=10,  # 漏检成本高
        false_positive_cost=1,   # 误检成本相对较低
        detection_threshold=0.04,
        confidence_level=0.95
    )
    
    # 训练模型并生成策略
    strategies = risk_model.fit_and_predict(df)
    
    # 打印风险感知的建议
    risk_model.print_risk_aware_recommendations(strategies)
    
    return risk_model, strategies

def visualize_risk_analysis(strategies):
    """可视化风险分析结果"""
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('风险感知检测策略分析', fontsize=16)

    # 1. 各BMI组的最优检测时间对比
    bmi_groups = list(strategies.keys())
    primary_times = [strategies[group]['primary_strategy']['primary_time'] for group in bmi_groups]
    success_rates = [strategies[group]['primary_strategy']['success_rate'] for group in bmi_groups]

    axes[0, 0].bar(bmi_groups, primary_times, color='skyblue', alpha=0.7)
    axes[0, 0].set_title('各BMI组最优检测时间')
    axes[0, 0].set_ylabel('检测时间 (周)')
    axes[0, 0].tick_params(axis='x', rotation=45)

    # 2. 成功率对比
    axes[0, 1].bar(bmi_groups, success_rates, color='lightgreen', alpha=0.7)
    axes[0, 1].set_title('各BMI组预期成功率')
    axes[0, 1].set_ylabel('成功率')
    axes[0, 1].tick_params(axis='x', rotation=45)
    axes[0, 1].set_ylim(0, 1)

    # 3. 风险评分对比
    risk_scores = [strategies[group]['primary_strategy']['total_risk'] for group in bmi_groups]
    colors = ['green' if r < 2 else 'orange' if r < 5 else 'red' for r in risk_scores]

    axes[1, 0].bar(bmi_groups, risk_scores, color=colors, alpha=0.7)
    axes[1, 0].set_title('各BMI组风险评分')
    axes[1, 0].set_ylabel('总风险评分')
    axes[1, 0].tick_params(axis='x', rotation=45)

    # 4. 检测窗口可视化
    for i, group in enumerate(bmi_groups):
        window = strategies[group]['primary_strategy']['window']
        axes[1, 1].barh(i, window[1] - window[0], left=window[0],
                       alpha=0.6, label=group)

    axes[1, 1].set_title('各BMI组检测窗口')
    axes[1, 1].set_xlabel('孕周')
    axes[1, 1].set_yticks(range(len(bmi_groups)))
    axes[1, 1].set_yticklabels(bmi_groups)

    plt.tight_layout()
    plt.show()

def sensitivity_analysis_with_risk(df, base_model):
    """带风险考虑的敏感性分析"""
    print("\n" + "="*60)
    print("风险敏感性分析")
    print("="*60)

    # 1. 成本权重敏感性分析
    print("\n1. 成本权重敏感性分析:")
    cost_ratios = [1, 5, 10, 20]  # 假阴性成本 / 假阳性成本的比率

    for ratio in cost_ratios:
        print(f"\n假阴性/假阳性成本比率: {ratio}:1")

        risk_model = RiskAwareDecisionTree(
            false_negative_cost=ratio,
            false_positive_cost=1,
            detection_threshold=0.04,
            confidence_level=0.95
        )

        strategies = risk_model.fit_and_predict(df)

        for bmi_group, strategy in strategies.items():
            primary = strategy['primary_strategy']
            print(f"  {bmi_group}: {primary['primary_time']:.1f}周 "
                  f"(成功率:{primary['success_rate']:.2%}, "
                  f"风险:{primary['total_risk']:.2f})")

    # 2. 检测阈值敏感性分析
    print("\n2. 检测阈值敏感性分析:")
    thresholds = [0.035, 0.04, 0.045, 0.05]

    for threshold in thresholds:
        print(f"\n检测阈值: {threshold}")

        risk_model = RiskAwareDecisionTree(
            false_negative_cost=10,
            false_positive_cost=1,
            detection_threshold=threshold,
            confidence_level=0.95
        )

        strategies = risk_model.fit_and_predict(df)

        for bmi_group, strategy in strategies.items():
            primary = strategy['primary_strategy']
            print(f"  {bmi_group}: {primary['primary_time']:.1f}周 "
                  f"(成功率:{primary['success_rate']:.2%}, "
                  f"风险:{primary['total_risk']:.2f})")

def generate_clinical_guidelines(strategies):
    """生成临床指导原则"""
    print("\n" + "="*80)
    print("临床检测指导原则")
    print("="*80)

    print("\n【基于风险最小化的检测策略】")

    for bmi_group, strategy in strategies.items():
        primary = strategy['primary_strategy']
        backup = strategy['backup_strategy']

        print(f"\n{bmi_group} BMI组患者:")
        print(f"1. 首选检测时间: 孕{primary['window'][0]}-{primary['window'][1]}周")
        print(f"   - 最佳时间点: 孕{primary['primary_time']:.1f}周")
        print(f"   - 预期成功率: {primary['success_rate']:.1%}")

        if backup:
            print(f"2. 如首次检测失败，备选时间: 孕{backup['window'][0]}-{backup['window'][1]}周")
            print(f"   - 备选时间点: 孕{backup['backup_time']:.1f}周")
            print(f"   - 预期成功率: {backup['success_rate']:.1%}")
        else:
            print(f"2. 如首次检测失败，建议延后2-3周后重新检测")

        # 风险等级建议
        if primary['total_risk'] < 2:
            print(f"3. 风险等级: 低风险 - 可按标准流程进行")
        elif primary['total_risk'] < 5:
            print(f"3. 风险等级: 中等风险 - 建议密切监测")
        else:
            print(f"3. 风险等级: 高风险 - 建议多次检测确认")

    print(f"\n【注意事项】")
    print(f"- 本策略基于风险最小化原则制定")
    print(f"- 考虑了检测失败的临床后果")
    print(f"- 建议结合患者具体情况调整")
    print(f"- 如有疑问，建议咨询专科医生")

if __name__ == '__main__':
    model, strategies = main()

    # 可视化分析结果
    visualize_risk_analysis(strategies)

    # 敏感性分析
    df = pd.read_excel(r'D:\数学建模\2025国赛C题\男胎数据.xlsx')
    sensitivity_analysis_with_risk(df, model)

    # 生成临床指导原则
    generate_clinical_guidelines(strategies)
