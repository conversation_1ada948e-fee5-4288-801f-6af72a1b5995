import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.neighbors import KNeighborsRegressor
from sklearn.metrics import mean_squared_error, r2_score
import matplotlib.pyplot as plt

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# KNN回归
def knn_regression(df, k=5):
    # 数据预处理
    df_clean = df.dropna(subset=['检测孕周', '孕妇BMI', 'Y染色体浓度'])

    # 特征选择
    features = ['孕妇BMI']
    X = df_clean[features]
    y = df_clean['检测孕周']  # 目标变量：最佳检测时间

    # 数据分割和模型训练
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
    knn_reg = KNeighborsRegressor(n_neighbors=k)
    knn_reg.fit(X_train, y_train)

    # 预测和评估
    y_pred = knn_reg.predict(X_test)
    mse = mean_squared_error(y_test, y_pred)
    r2 = r2_score(y_test, y_pred)

    print("\nKNN回归结果：")
    print(f"均方误差 (MSE): {mse:.4f}")
    print(f"决定系数 (R²): {r2:.4f}")

    # 可视化预测结果
    plt.figure(figsize=(8, 6))
    plt.scatter(y_test, y_pred, alpha=0.6)
    plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], c='r', lw=2)
    plt.xlabel('实际最佳检测时间')
    plt.ylabel('预测最佳检测时间')
    plt.title(f'KNN回归预测结果 (R² = {r2:.4f})')
    plt.tight_layout()
    plt.show()

    return knn_reg

# BMI分组最佳检测时间
def bmi_group_optimal_time_knn(df, knn_reg):
    # 数据预处理
    df_clean = df.dropna(subset=['孕妇代码', '检测孕周', '孕妇BMI', 'Y染色体浓度'])

    # 定义BMI分组（按照题目要求）
    def get_bmi_group(bmi):
        if 20 <= bmi < 28:
            return '[20,28)'
        elif 28 <= bmi < 32:
            return '[28,32)'
        elif 32 <= bmi < 36:
            return '[32,36)'
        elif 36 <= bmi < 40:
            return '[36,40)'
        elif bmi >= 40:
            return '≥40'
        else:
            return '<20'  # BMI小于20的情况

    df_clean = df_clean.copy()
    df_clean['BMI组'] = df_clean['孕妇BMI'].apply(get_bmi_group)

    # 为每个孕妇找到首次达标时间
    individual_data = []
    for _, group in df_clean.groupby('孕妇代码'):
        group = group.sort_values('检测孕周')
        mask = group['Y染色体浓度'] > 0.04
        if mask.any():
            optimal_time = group[mask]['检测孕周'].iloc[0]
        else:
            optimal_time = group['检测孕周'].iloc[-1]

        info = group.iloc[0]
        individual_data.append({
            '孕妇BMI': info['孕妇BMI'],
            'BMI组': info['BMI组'],
            '最佳检测时间': optimal_time
        })

    individual_df = pd.DataFrame(individual_data)

    print("\nBMI分组统计：")
    for group_name in individual_df['BMI组'].unique():
        group_data = individual_df[individual_df['BMI组'] == group_name]
        print(f"\n{group_name}组:")
        print(f"  样本数: {len(group_data)}")
        print(f"  平均最佳检测时间: {group_data['最佳检测时间'].mean():.1f}周")

    # 使用KNN为每个BMI组找最佳时间
    optimal_times_by_group = {}

    for group_name in individual_df['BMI组'].unique():
        group_data = individual_df[individual_df['BMI组'] == group_name]

        # 特征：BMI
        features = ['孕妇BMI']
        X = group_data[features]
        y = group_data['最佳检测时间']

        if len(group_data) > 10:  # 确保有足够样本
            # KNN回归
            avg_bmi = X.mean().values.reshape(1, -1)
            predicted_time = knn_reg.predict(avg_bmi)[0]

            optimal_times_by_group[group_name] = {
                '建议检测时间': round(predicted_time, 1),
                '样本数': len(group_data)
            }
        else:
            # 样本太少，使用平均值
            optimal_times_by_group[group_name] = {
                '建议检测时间': round(group_data['最佳检测时间'].mean(), 1),
                '样本数': len(group_data)
            }

    print("\n各BMI组最佳检测时间建议：")
    for group, info in optimal_times_by_group.items():
        print(f"\n{group}:")
        print(f"  建议检测时间: {info['建议检测时间']}周")
        print(f"  基于样本数: {info['样本数']}")

    return optimal_times_by_group, individual_df

# KNN误差敏感性分析
def error_sensitivity_analysis_knn(df):
    print("\nKNN检测误差敏感性分析：")

    # 定义不同的误差水平
    error_levels = [0.005, 0.01, 0.02, 0.05]  # Y染色体浓度的误差范围
    results = {}

    # 获取基准结果（无误差）
    knn_reg_baseline = knn_regression(df, k=5)
    baseline_results, _ = bmi_group_optimal_time_knn(df, knn_reg_baseline)

    for error_level in error_levels:
        print(f"误差水平: ±{error_level:.3f}")

        # 添加误差后重新分析
        df_error = df.copy()
        noise = np.random.normal(0, error_level, len(df_error))
        df_error['Y染色体浓度'] = df_error['Y染色体浓度'] + noise
        df_error['Y染色体浓度'] = np.maximum(df_error['Y染色体浓度'], 0)  # 确保非负

        try:
            # 重新训练KNN模型
            knn_reg_error = knn_regression(df_error, k=5)
            error_results, _ = bmi_group_optimal_time_knn(df_error, knn_reg_error)
            results[error_level] = error_results

            # 输出当前误差水平的结果
            for group, info in error_results.items():
                baseline_time = baseline_results.get(group, {}).get('建议检测时间', 0)
                diff = info['建议检测时间'] - baseline_time
                print(f"  {group}: {info['建议检测时间']}周 (基准:{baseline_time}周, 差异:{diff:+.1f}周)")
        except Exception as e:
            print(f"  误差水平 {error_level} 分析失败: {e}")
        print()

    return results

# KNN阈值敏感性分析
def threshold_sensitivity_analysis_knn(df):
    print("\nKNN检测阈值敏感性分析：")

    # 不同的阈值水平
    thresholds = [0.035, 0.04, 0.045, 0.05]

    for threshold in thresholds:
        print(f"阈值: {threshold}")

        # 简化分析：直接计算不同阈值下的达标率
        df_clean = df.dropna(subset=['孕妇代码', '检测孕周', '孕妇BMI', 'Y染色体浓度'])
        df_clean = df_clean.copy()

        def get_bmi_group(bmi):
            if 20 <= bmi < 28: return '[20,28)'
            elif 28 <= bmi < 32: return '[28,32)'
            elif 32 <= bmi < 36: return '[32,36)'
            elif 36 <= bmi < 40: return '[36,40)'
            elif bmi >= 40: return '≥40'
            else: return '<20'

        df_clean['BMI组'] = df_clean['孕妇BMI'].apply(get_bmi_group)

        # 计算各BMI组在当前阈值下的达标率
        for bmi_group in ['[20,28)', '[28,32)', '[32,36)', '[36,40)', '≥40']:
            group_data = df_clean[df_clean['BMI组'] == bmi_group]
            if len(group_data) > 0:
                success_rate = (group_data['Y染色体浓度'] > threshold).mean()
                avg_time = group_data['检测孕周'].mean()
                print(f"  {bmi_group}: 平均{avg_time:.1f}周, 达标率{success_rate:.2%}")
        print()

    return thresholds

def main():
    # 加载数据
    df = pd.read_excel('2025国赛C题/男胎数据.xlsx')
    print(f"数据形状: {df.shape}")

    # KNN回归任务：预测最佳检测时间
    knn_reg = knn_regression(df, k=5)

    # BMI分组最佳检测时间分析
    optimal_times, _ = bmi_group_optimal_time_knn(df, knn_reg)

    # 总结建议
    print("基于KNN的BMI分组最佳检测时间")

    # 按BMI从小到大排序输出
    bmi_order = ['[20,28)', '[28,32)', '[32,36)', '[36,40)', '≥40']
    for group in bmi_order:
        if group in optimal_times:
            info = optimal_times[group]
            print(f"BMI {group}: {info['建议检测时间']}周 (样本数:{info['样本数']})")

if __name__ == '__main__':
    # 加载数据
    df1 = pd.read_excel('2025国赛C题/男胎数据.xlsx')

    # 运行主要分析
    main()

    # KNN检测误差敏感性分析
    error_results = error_sensitivity_analysis_knn(df1)

    # KNN检测阈值敏感性分析
    threshold_results = threshold_sensitivity_analysis_knn(df1)